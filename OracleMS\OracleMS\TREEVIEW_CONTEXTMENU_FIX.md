# TreeView 右鍵選單崩潰問題修復

## 問題描述

在 TreeView items 上按滑鼠右鍵會導致程式崩潰，錯誤訊息：
```
System.InvalidOperationException: '{DependencyProperty.UnsetValue}' 對屬性 'ContextMenu' 來說，並非有效的值。
```

## 問題分析

經過程式碼分析，發現以下幾個導致崩潰的問題：

### 1. ContextMenu 綁定問題
在 `ObjectExplorerView.xaml` 中，TreeViewItem 的 ContextMenu 直接設定為靜態資源：
```xml
<Setter Property="ContextMenu" Value="{StaticResource DatabaseObjectContextMenu}"/>
```

### 2. 資料綁定失敗
ContextMenu 中的 MenuItem 使用了 `IsTableObject` 屬性進行 Visibility 綁定：
```xml
<MenuItem Visibility="{Binding IsTableObject, Converter={StaticResource BooleanToVisibilityConverter}}"/>
```

當 TreeViewItem 的 DataContext 是 placeholder 節點或其他特殊情況時，綁定可能失敗並返回 `DependencyProperty.UnsetValue`。

### 3. Converter 未處理 UnsetValue
`BooleanToVisibilityConverter` 沒有處理 `DependencyProperty.UnsetValue` 的情況。

## 修復方案

### 1. 動態創建 ContextMenu
完全移除 XAML 中的靜態 ContextMenu 綁定，改為在程式碼中動態創建：

```csharp
private ContextMenu? CreateContextMenuForNode(DatabaseObjectNode node)
{
    try
    {
        var contextMenu = new ContextMenu();

        // 根據節點類型動態添加選單項目
        if (node.IsTableObject)
        {
            // 添加資料表特定選單項目
        }

        if (!node.IsPlaceholder)
        {
            // 添加一般選單項目
        }

        return contextMenu;
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"創建 ContextMenu 時發生錯誤: {ex.Message}");
        return null;
    }
}
```

### 2. 安全的事件處理
在 ContextMenuOpening 事件中進行安全檢查：

```csharp
private void OnTreeViewItemContextMenuOpening(object sender, ContextMenuEventArgs e)
{
    try
    {
        if (sender is TreeViewItem treeViewItem)
        {
            if (treeViewItem.DataContext is DatabaseObjectNode node)
            {
                // 如果是 placeholder 節點，取消顯示 ContextMenu
                if (node.IsPlaceholder)
                {
                    e.Handled = true;
                    return;
                }

                // 動態創建 ContextMenu
                var contextMenu = CreateContextMenuForNode(node);
                if (contextMenu != null)
                {
                    treeViewItem.ContextMenu = contextMenu;
                }
                else
                {
                    e.Handled = true;
                }
            }
        }
    }
    catch (Exception ex)
    {
        e.Handled = true;
    }
}
```

### 3. 強化 IsTableObject 屬性
在 `DatabaseObjectNode` 中改善 `IsTableObject` 屬性：

```csharp
public bool IsTableObject
{
    get
    {
        try
        {
            return ObjectType == DatabaseObjectType.Table && !IsFolder && !IsPlaceholder;
        }
        catch
        {
            return false;
        }
    }
}
```

### 4. 改善 BooleanToVisibilityConverter
添加對 `DependencyProperty.UnsetValue` 的處理：

```csharp
public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
{
    try
    {
        // 檢查是否為 DependencyProperty.UnsetValue
        if (value == DependencyProperty.UnsetValue)
        {
            return Visibility.Collapsed;
        }
        
        if (value is bool boolValue)
        {
            var result = Invert ? !boolValue : boolValue;
            return result ? Visibility.Visible : Visibility.Collapsed;
        }
        
        // 嘗試轉換其他類型
        if (value != null && bool.TryParse(value.ToString(), out bool parsedValue))
        {
            var result = Invert ? !parsedValue : parsedValue;
            return result ? Visibility.Visible : Visibility.Collapsed;
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"BooleanToVisibilityConverter 轉換錯誤: {ex.Message}");
    }
    
    return Visibility.Collapsed;
}
```

### 5. 添加 ContextMenu 開啟事件處理
在 `ObjectExplorerView.xaml.cs` 中添加安全的 ContextMenu 處理：

```csharp
private void OnContextMenuOpening(object sender, ContextMenuEventArgs e)
{
    try
    {
        if (e.OriginalSource is FrameworkElement element)
        {
            var treeViewItem = FindParent<TreeViewItem>(element);
            if (treeViewItem?.DataContext is DatabaseObjectNode node)
            {
                // 如果是 placeholder 節點，取消顯示 ContextMenu
                if (node.IsPlaceholder)
                {
                    e.Handled = true;
                    return;
                }
            }
            else
            {
                // 如果不是有效的節點，取消顯示 ContextMenu
                e.Handled = true;
                return;
            }
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"ContextMenu 開啟時發生錯誤: {ex.Message}");
        e.Handled = true;
    }
}
```

## 修改的檔案

1. **ObjectExplorerView.xaml** - 修改 TreeViewItem 樣式和 ContextMenu 綁定
2. **ObjectExplorerView.xaml.cs** - 添加 ContextMenu 開啟事件處理
3. **ObjectExplorerViewModel.cs** - 強化 IsTableObject 屬性
4. **Converters.cs** - 改善 BooleanToVisibilityConverter

## 預期效果

修復後，TreeView 右鍵選單應該能夠：
- ✅ 安全地處理 placeholder 節點（不顯示選單）
- ✅ 正確顯示適當的選單項目
- ✅ 避免 DependencyProperty.UnsetValue 錯誤
- ✅ 在任何情況下都不會導致程式崩潰
- ✅ 動態根據節點類型創建選單內容

## 最終修復方案

採用了**動態填充 ContextMenu** 的方法：

1. **XAML 設定空的 ContextMenu**：
   ```xml
   <Setter Property="ContextMenu">
       <Setter.Value>
           <ContextMenu/>
       </Setter.Value>
   </Setter>
   ```

2. **事件處理中動態填充**：
   ```csharp
   private void OnTreeViewItemContextMenuOpening(object sender, ContextMenuEventArgs e)
   {
       if (sender is TreeViewItem treeViewItem && treeViewItem.DataContext is DatabaseObjectNode node)
       {
           if (node.IsPlaceholder)
           {
               e.Handled = true; // 取消顯示
               return;
           }

           treeViewItem.ContextMenu.Items.Clear();
           PopulateContextMenu(treeViewItem.ContextMenu, node);
       }
   }
   ```

3. **根據節點類型動態添加選單項目**：
   - 資料表節點：查看資料、設計資料表、產生腳本等
   - 一般節點：產生腳本、複製物件名稱、重新整理
   - Placeholder 節點：不顯示選單

## 測試建議

1. **基本功能測試**：
   - 在不同類型的 TreeView 節點上右鍵點擊
   - 在 placeholder 節點（如"載入中..."）上右鍵點擊，確認不顯示選單
   - 在資料表節點上右鍵點擊，確認顯示正確的選單項目
   - 在資料夾節點上右鍵點擊，確認顯示適當的選單項目

2. **穩定性測試**：
   - 快速連續右鍵點擊不同節點
   - 在載入過程中右鍵點擊節點
   - 同時展開多個節點並右鍵點擊

3. **功能測試**：
   - 點擊選單項目，確認功能正常執行
   - 檢查 Debug 輸出，確認沒有錯誤訊息
