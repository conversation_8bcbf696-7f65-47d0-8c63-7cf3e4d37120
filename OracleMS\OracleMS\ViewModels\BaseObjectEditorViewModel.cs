using System;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Exceptions;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 資料庫物件編輯器 ViewModel 的基底類別
    /// </summary>
    public abstract class BaseObjectEditorViewModel : ViewModelBase, ISaveable, IDisposable
    {
        protected readonly IDatabaseService _databaseService;
        protected readonly IScriptGeneratorService _scriptGeneratorService;
        protected readonly IObjectEditorService _objectEditorService;
        protected readonly Func<IDbConnection?> _getConnection;
        protected readonly ObjectEditorErrorHandler _errorHandler;
        protected readonly EditorStateManager _stateManager;
        protected readonly ILogger _logger;
        protected ProgressIndicator? _progressIndicator;
        protected CancellationTokenSource? _cancellationTokenSource;
        protected bool _isInitializing = true;

        private string _objectName = string.Empty;
        /// <summary>
        /// 物件名稱
        /// </summary>
        public string ObjectName
        {
            get => _objectName;
            protected set => SetProperty(ref _objectName, value);
        }

        private DatabaseObjectType _objectType;
        /// <summary>
        /// 物件類型
        /// </summary>
        public DatabaseObjectType ObjectType
        {
            get => _objectType;
            protected set => SetProperty(ref _objectType, value);
        }

        private bool _hasUnsavedChanges;
        /// <summary>
        /// 是否有未儲存的變更
        /// </summary>
        public bool HasUnsavedChanges
        {
            get => _hasUnsavedChanges;
            protected set
            {
                if (SetProperty(ref _hasUnsavedChanges, value))
                {
                    // 通知命令重新評估
                    NotifyCanExecuteChanged();
                }
            }
        }

        private bool _isLoading;
        /// <summary>
        /// 是否正在載入
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            protected set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    // 通知命令重新評估
                    NotifyCanExecuteChanged();
                }
            }
        }

        private bool _isSaving;
        /// <summary>
        /// 是否正在儲存
        /// </summary>
        public bool IsSaving
        {
            get => _isSaving;
            protected set
            {
                if (SetProperty(ref _isSaving, value))
                {
                    // 通知命令重新評估
                    NotifyCanExecuteChanged();
                }
            }
        }

        private string _statusMessage = string.Empty;
        /// <summary>
        /// 狀態訊息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            protected set => SetProperty(ref _statusMessage, value);
        }

        private string _errorMessage = string.Empty;
        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            protected set => SetProperty(ref _errorMessage, value);
        }

        private bool _hasError;
        /// <summary>
        /// 是否有錯誤
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            protected set => SetProperty(ref _hasError, value);
        }
        
        private double _progress;
        /// <summary>
        /// 進度值 (0-100)
        /// </summary>
        public double Progress
        {
            get => _progress;
            protected set => SetProperty(ref _progress, value);
        }
        
        private bool _isIndeterminateProgress;
        /// <summary>
        /// 是否為不確定進度
        /// </summary>
        public bool IsIndeterminateProgress
        {
            get => _isIndeterminateProgress;
            protected set => SetProperty(ref _isIndeterminateProgress, value);
        }
        
        private bool _showProgress;
        /// <summary>
        /// 是否顯示進度
        /// </summary>
        public bool ShowProgress
        {
            get => _showProgress;
            protected set => SetProperty(ref _showProgress, value);
        }
        
        private int _selectedTabIndex;
        /// <summary>
        /// 選取的索引標籤
        /// </summary>
        public int SelectedTabIndex
        {
            get => _selectedTabIndex;
            set
            {
                if (SetProperty(ref _selectedTabIndex, value))
                {
                    SaveEditorStateAsync().ConfigureAwait(false);
                }
            }
        }
        
        private int _cursorPosition;
        /// <summary>
        /// 游標位置
        /// </summary>
        public int CursorPosition
        {
            get => _cursorPosition;
            set
            {
                if (SetProperty(ref _cursorPosition, value))
                {
                    SaveEditorStateAsync().ConfigureAwait(false);
                }
            }
        }

        /// <summary>
        /// 儲存命令
        /// </summary>
        public ICommand SaveCommand { get; }

        /// <summary>
        /// 重新載入命令
        /// </summary>
        public ICommand RefreshCommand { get; }

        /// <summary>
        /// 產生腳本命令
        /// </summary>
        public ICommand GenerateScriptCommand { get; }

        /// <summary>
        /// 取消命令
        /// </summary>
        public ICommand CancelCommand { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="objectName">物件名稱</param>
        /// <param name="objectType">物件類型</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        protected BaseObjectEditorViewModel(
            string objectName,
            DatabaseObjectType objectType,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            ObjectName = objectName ?? throw new ArgumentNullException(nameof(objectName));
            ObjectType = objectType;
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _scriptGeneratorService = scriptGeneratorService ?? throw new ArgumentNullException(nameof(scriptGeneratorService));
            _objectEditorService = objectEditorService ?? throw new ArgumentNullException(nameof(objectEditorService));
            _getConnection = getConnection ?? throw new ArgumentNullException(nameof(getConnection));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _errorHandler = new ObjectEditorErrorHandler(_logger);
            _stateManager = new EditorStateManager(_logger);

            // 初始化命令
            SaveCommand = new AsyncRelayCommand(OnSaveAsync, CanSave);
            RefreshCommand = new AsyncRelayCommand(OnRefreshAsync, CanRefresh);
            GenerateScriptCommand = new AsyncRelayCommand(OnGenerateScriptAsync, CanGenerateScript);
            CancelCommand = new RelayCommand(OnCancel, CanCancel);
            
            // 初始化進度指示器
            _progressIndicator = new ProgressIndicator(
                statusMessage => StatusMessage = statusMessage,
                progress => Progress = progress);

            // 保持初始化狀態，直到 InitializeAsync 完成
            // _isInitializing 將在 InitializeAsync 結束時設置為 false
        }

        /// <summary>
        /// 初始化 ViewModel
        /// </summary>
        /// <returns>非同步工作</returns>
        public virtual async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                ShowProgress = true;
                IsIndeterminateProgress = true;
                HasError = false;
                ErrorMessage = string.Empty;

                // 啟動進度指示
                _progressIndicator?.Start($"載入{GetObjectTypeText()}");

                // 載入物件
                await LoadObjectAsync();

                // 載入編輯器狀態
                await LoadEditorStateAsync();

                // 完成載入
                _progressIndicator?.Complete($"{GetObjectTypeText()}已載入");
                HasUnsavedChanges = false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "載入");
                _progressIndicator?.Fail(ex.Message);
            }
            finally
            {
                IsLoading = false;
                ShowProgress = false;

                // 初始化完成，開始追蹤變更
                _isInitializing = false;
            }
        }
        
        /// <summary>
        /// 取得物件類型文字
        /// </summary>
        /// <returns>物件類型文字</returns>
        protected string GetObjectTypeText()
        {
            return ObjectType switch
            {
                DatabaseObjectType.Table => "資料表",
                DatabaseObjectType.View => "檢視表",
                DatabaseObjectType.Procedure => "預存程序",
                DatabaseObjectType.Function => "函數",
                DatabaseObjectType.Package => "套件",
                DatabaseObjectType.Sequence => "序列",
                DatabaseObjectType.Trigger => "觸發器",
                DatabaseObjectType.Index => "索引",
                _ => "物件"
            };
        }

        /// <summary>
        /// 儲存物件
        /// </summary>
        public virtual async void Save()
        {
            await OnSaveAsync();
        }

        /// <summary>
        /// 儲存物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected virtual async Task OnSaveAsync()
        {
            if (IsSaving || IsLoading)
                return;

            try
            {
                IsSaving = true;
                ShowProgress = true;
                IsIndeterminateProgress = false;
                HasError = false;
                ErrorMessage = string.Empty;
                
                // 啟動進度指示
                _progressIndicator?.Start($"儲存{GetObjectTypeText()}", false);
                
                // 驗證物件
                _progressIndicator?.UpdateStatus("正在驗證物件...");
                _progressIndicator?.UpdateProgress(10);
                
                var validationResult = ValidateObject();
                if (!validationResult.IsValid)
                {
                    HasError = true;
                    ErrorMessage = string.Join("\n", validationResult.Errors);
                    _progressIndicator?.Fail("驗證錯誤");
                    return;
                }
                
                // 準備儲存
                _progressIndicator?.UpdateStatus("正在準備儲存...");
                _progressIndicator?.UpdateProgress(30);
                await Task.Delay(100); // 給 UI 更新的時間
                
                // 儲存物件
                await SaveObjectAsync();
                
                // 儲存編輯器狀態
                await SaveEditorStateAsync();
                
                // 完成儲存
                _progressIndicator?.Complete($"{GetObjectTypeText()}已儲存");
                HasUnsavedChanges = false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "儲存");
                _progressIndicator?.Fail(ex.Message);
            }
            finally
            {
                IsSaving = false;
                ShowProgress = false;
            }
        }

        /// <summary>
        /// 重新載入物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected virtual async Task OnRefreshAsync()
        {
            if (IsLoading || IsSaving)
                return;

            // 如果有未儲存的變更，詢問使用者是否要放棄變更
            if (HasUnsavedChanges)
            {
                var result = System.Windows.MessageBox.Show(
                    $"物件 '{ObjectName}' 有未儲存的變更。\n\n是否要放棄變更並重新載入？",
                    "確認重新載入",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question,
                    System.Windows.MessageBoxResult.No);

                if (result != System.Windows.MessageBoxResult.Yes)
                {
                    return;
                }
            }

            await InitializeAsync();
        }

        /// <summary>
        /// 產生腳本
        /// </summary>
        /// <returns>非同步工作</returns>
        protected virtual async Task OnGenerateScriptAsync()
        {
            try
            {
                ShowProgress = true;
                IsIndeterminateProgress = false;
                HasError = false;
                ErrorMessage = string.Empty;
                
                // 啟動進度指示
                _progressIndicator?.Start("產生腳本", false);
                
                // 產生腳本
                _progressIndicator?.UpdateProgress(30);
                var script = GenerateScript();
                
                // 模擬一些處理時間
                await Task.Delay(300);
                _progressIndicator?.UpdateProgress(70);
                
                // 實際應用中，這裡可能會開啟一個新的查詢編輯器或顯示腳本對話框
                await Task.Delay(200);
                
                // 完成
                _progressIndicator?.Complete("腳本已產生");
            }
            catch (Exception ex)
            {
                HandleError(ex, "產生腳本");
                _progressIndicator?.Fail(ex.Message);
            }
            finally
            {
                ShowProgress = false;
            }
        }

        /// <summary>
        /// 取消操作
        /// </summary>
        protected virtual void OnCancel()
        {
            _cancellationTokenSource?.Cancel();
            _progressIndicator?.Cancel();
            StatusMessage = "操作已取消";
            ShowProgress = false;
        }

        /// <summary>
        /// 處理錯誤
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        protected virtual void HandleError(Exception ex, string operation)
        {
            HasError = true;
            
            // 使用錯誤處理器格式化錯誤訊息
            var formattedMessage = _errorHandler.FormatErrorMessage(ex, operation, ObjectName, ObjectType);
            ErrorMessage = formattedMessage;
            StatusMessage = $"{operation}失敗";

            // 收集診斷資訊
            var errorResult = _errorHandler.CreateErrorResult(ex, operation, ObjectName, ObjectType);
            
            // 記錄詳細錯誤資訊到診斷輸出
            System.Diagnostics.Debug.WriteLine($"[{GetType().Name}] {operation}錯誤：\n{errorResult.ErrorDetails}");
        }

        /// <summary>
        /// 新增訊息
        /// </summary>
        /// <param name="message">訊息</param>
        protected virtual void AddMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            StatusMessage = $"[{timestamp}] {message}";
        }

        /// <summary>
        /// 是否可以儲存
        /// </summary>
        /// <returns>是否可以儲存</returns>
        protected virtual bool CanSave()
        {
            return !IsLoading && !IsSaving && HasUnsavedChanges && _getConnection() != null;
        }

        /// <summary>
        /// 是否可以重新載入
        /// </summary>
        /// <returns>是否可以重新載入</returns>
        protected virtual bool CanRefresh()
        {
            return !IsLoading && !IsSaving && _getConnection() != null;
        }

        /// <summary>
        /// 是否可以產生腳本
        /// </summary>
        /// <returns>是否可以產生腳本</returns>
        protected virtual bool CanGenerateScript()
        {
            return !IsLoading && !IsSaving && _getConnection() != null;
        }

        /// <summary>
        /// 是否可以取消
        /// </summary>
        /// <returns>是否可以取消</returns>
        protected virtual bool CanCancel()
        {
            return IsLoading || IsSaving;
        }

        /// <summary>
        /// 載入物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected abstract Task LoadObjectAsync();

        /// <summary>
        /// 儲存物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected abstract Task SaveObjectAsync();

        /// <summary>
        /// 產生腳本
        /// </summary>
        /// <returns>腳本</returns>
        protected abstract string GenerateScript();

        /// <summary>
        /// 驗證物件
        /// </summary>
        /// <returns>驗證結果</returns>
        protected abstract ValidationResult ValidateObject();

        #region IDisposable

        private bool _disposed = false;

        /// <summary>
        /// 釋放資源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 釋放資源
        /// </summary>
        /// <param name="disposing">是否正在釋放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // 先設定 _disposed 標記，避免其他操作繼續執行
                        _disposed = true;

                        // 取消所有進行中的操作
                        _cancellationTokenSource?.Cancel();

                        // 儲存最終狀態（但不等待，避免死鎖）
                        if (HasUnsavedChanges)
                        {
                            try
                            {
                                // 使用 Task.Run 避免在 UI 執行緒上同步等待
                                Task.Run(async () =>
                                {
                                    try
                                    {
                                        await SaveEditorStateAsync();
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"儲存編輯器狀態時發生錯誤: {ex.Message}");
                                    }
                                });
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"啟動儲存狀態任務時發生錯誤: {ex.Message}");
                            }
                        }

                        // 釋放受控資源
                        try
                        {
                            _cancellationTokenSource?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"釋放 CancellationTokenSource 時發生錯誤: {ex.Message}");
                        }

                        try
                        {
                            _progressIndicator?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"釋放 ProgressIndicator 時發生錯誤: {ex.Message}");
                        }

                        // 釋放狀態管理器
                        try
                        {
                            _stateManager?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"釋放 StateManager 時發生錯誤: {ex.Message}");
                        }

                        // 執行子類別的清理邏輯
                        try
                        {
                            OnDisposing();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"執行子類別清理邏輯時發生錯誤: {ex.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"釋放資源時發生錯誤: {ex.Message}");
                        // 確保 _disposed 被設定，即使發生錯誤
                        _disposed = true;
                    }
                }
                else
                {
                    // 釋放非受控資源
                    _disposed = true;
                }
            }
        }
        
        /// <summary>
        /// 在釋放資源時執行
        /// </summary>
        protected virtual void OnDisposing()
        {
            // 基底類別不做任何事，由子類別實作
        }

        #endregion
        /// <summary>
        /// 儲存編輯器狀態
        /// </summary>
        /// <returns>非同步工作</returns>
        protected virtual async Task SaveEditorStateAsync()
        {
            if (_isInitializing)
                return;
            
            try
            {
                var state = new EditorState
                {
                    ObjectType = ObjectType,
                    ObjectName = ObjectName,
                    LastModified = DateTime.Now,
                    CursorPosition = CursorPosition,
                    SelectedTabIndex = SelectedTabIndex,
                    CustomState = GetCustomState()
                };
                
                await _stateManager.SaveStateAsync(ObjectType, ObjectName, state);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"儲存編輯器狀態失敗: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 載入編輯器狀態
        /// </summary>
        /// <returns>非同步工作</returns>
        protected virtual async Task LoadEditorStateAsync()
        {
            try
            {
                var state = await _stateManager.LoadStateAsync(ObjectType, ObjectName);
                if (state != null)
                {
                    _isInitializing = true;
                    
                    CursorPosition = state.CursorPosition;
                    SelectedTabIndex = state.SelectedTabIndex;
                    
                    ApplyCustomState(state.CustomState);
                    
                    _isInitializing = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入編輯器狀態失敗: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 刪除編輯器狀態
        /// </summary>
        /// <returns>非同步工作</returns>
        protected virtual async Task DeleteEditorStateAsync()
        {
            try
            {
                await _stateManager.DeleteStateAsync(ObjectType, ObjectName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刪除編輯器狀態失敗: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 取得自訂狀態
        /// </summary>
        /// <returns>自訂狀態</returns>
        protected virtual Dictionary<string, object> GetCustomState()
        {
            return new Dictionary<string, object>();
        }
        
        /// <summary>
        /// 套用自訂狀態
        /// </summary>
        /// <param name="customState">自訂狀態</param>
        protected virtual void ApplyCustomState(Dictionary<string, object> customState)
        {
            // 基底類別不做任何事，由子類別實作
        }

        /// <summary>
        /// 通知命令重新評估 CanExecute 狀態
        /// </summary>
        protected virtual void NotifyCanExecuteChanged()
        {
            // 確保在 UI 執行緒上執行
            if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == true)
            {
                NotifyCanExecuteChangedCore();
            }
            else
            {
                System.Windows.Application.Current?.Dispatcher.BeginInvoke(new Action(NotifyCanExecuteChangedCore));
            }
        }

        /// <summary>
        /// 核心命令通知邏輯
        /// </summary>
        private void NotifyCanExecuteChangedCore()
        {
            if (SaveCommand is AsyncRelayCommand saveCmd)
                saveCmd.NotifyCanExecuteChanged();

            if (RefreshCommand is AsyncRelayCommand refreshCmd)
                refreshCmd.NotifyCanExecuteChanged();

            if (GenerateScriptCommand is AsyncRelayCommand generateCmd)
                generateCmd.NotifyCanExecuteChanged();

            if (CancelCommand is RelayCommand cancelCmd)
                cancelCmd.NotifyCanExecuteChanged();
        }
    }
}