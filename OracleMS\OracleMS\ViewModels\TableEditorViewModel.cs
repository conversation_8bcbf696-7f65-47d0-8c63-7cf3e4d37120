using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 資料表編輯器 ViewModel
    /// </summary>
    public class TableEditorViewModel : BaseObjectEditorViewModel
    {
        private TableDefinition _tableDefinition;
        private ColumnDefinition _selectedColumn;
        private IndexDefinition _selectedIndex;
        private ConstraintDefinition _selectedConstraint;
        private TriggerDefinition _selectedTrigger;
        private string _ddlPreview = string.Empty;

        /// <summary>
        /// 資料表定義
        /// </summary>
        public TableDefinition TableDefinition
        {
            get => _tableDefinition;
            private set => SetProperty(ref _tableDefinition, value);
        }

        /// <summary>
        /// 選取的欄位
        /// </summary>
        public ColumnDefinition SelectedColumn
        {
            get => _selectedColumn;
            set => SetProperty(ref _selectedColumn, value);
        }

        /// <summary>
        /// 選取的索引
        /// </summary>
        public IndexDefinition SelectedIndex
        {
            get => _selectedIndex;
            set => SetProperty(ref _selectedIndex, value);
        }

        /// <summary>
        /// 選取的約束條件
        /// </summary>
        public ConstraintDefinition SelectedConstraint
        {
            get => _selectedConstraint;
            set => SetProperty(ref _selectedConstraint, value);
        }

        /// <summary>
        /// 選取的觸發器
        /// </summary>
        public TriggerDefinition SelectedTrigger
        {
            get => _selectedTrigger;
            set => SetProperty(ref _selectedTrigger, value);
        }

        /// <summary>
        /// DDL 預覽
        /// </summary>
        public string DdlPreview
        {
            get => _ddlPreview;
            private set => SetProperty(ref _ddlPreview, value);
        }

        /// <summary>
        /// 可用的資料類型清單
        /// </summary>
        public List<string> DataTypes { get; } = new List<string>
        {
            "VARCHAR2", "NVARCHAR2", "CHAR", "NCHAR", "NUMBER", "DATE", "TIMESTAMP", 
            "TIMESTAMP WITH TIME ZONE", "TIMESTAMP WITH LOCAL TIME ZONE", "INTERVAL YEAR TO MONTH", 
            "INTERVAL DAY TO SECOND", "BINARY_FLOAT", "BINARY_DOUBLE", "FLOAT", "LONG", "RAW", 
            "LONG RAW", "BLOB", "CLOB", "NCLOB", "BFILE", "ROWID", "UROWID", "XMLType"
        };

        /// <summary>
        /// 新增欄位命令
        /// </summary>
        public ICommand AddColumnCommand { get; }

        /// <summary>
        /// 刪除欄位命令
        /// </summary>
        public ICommand DeleteColumnCommand { get; }

        /// <summary>
        /// 上移欄位命令
        /// </summary>
        public ICommand MoveColumnUpCommand { get; }

        /// <summary>
        /// 下移欄位命令
        /// </summary>
        public ICommand MoveColumnDownCommand { get; }

        /// <summary>
        /// 新增索引命令
        /// </summary>
        public ICommand AddIndexCommand { get; }

        /// <summary>
        /// 刪除索引命令
        /// </summary>
        public ICommand DeleteIndexCommand { get; }

        /// <summary>
        /// 編輯索引命令
        /// </summary>
        public ICommand EditIndexCommand { get; }

        /// <summary>
        /// 新增約束條件命令
        /// </summary>
        public ICommand AddConstraintCommand { get; }

        /// <summary>
        /// 刪除約束條件命令
        /// </summary>
        public ICommand DeleteConstraintCommand { get; }

        /// <summary>
        /// 編輯約束條件命令
        /// </summary>
        public ICommand EditConstraintCommand { get; }

        /// <summary>
        /// 新增觸發器命令
        /// </summary>
        public ICommand AddTriggerCommand { get; }

        /// <summary>
        /// 刪除觸發器命令
        /// </summary>
        public ICommand DeleteTriggerCommand { get; }

        /// <summary>
        /// 編輯觸發器命令
        /// </summary>
        public ICommand EditTriggerCommand { get; }

        /// <summary>
        /// 複製 DDL 命令
        /// </summary>
        public ICommand CopyDdlCommand { get; }

        /// <summary>
        /// 儲存 DDL 命令
        /// </summary>
        public ICommand SaveDdlCommand { get; }

        /// <summary>
        /// 執行 DDL 命令
        /// </summary>
        public ICommand ExecuteDdlCommand { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="tableName">資料表名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        public TableEditorViewModel(
            string tableName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(tableName, DatabaseObjectType.Table, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            // 初始化資料表定義
            _tableDefinition = new TableDefinition { Name = tableName };

            // 初始化命令
            AddColumnCommand = new RelayCommand(AddColumn);
            DeleteColumnCommand = new RelayCommand<ColumnDefinition>(DeleteColumn, CanDeleteColumn);
            MoveColumnUpCommand = new RelayCommand<ColumnDefinition>(MoveColumnUp, CanMoveColumnUp);
            MoveColumnDownCommand = new RelayCommand<ColumnDefinition>(MoveColumnDown, CanMoveColumnDown);

            AddIndexCommand = new RelayCommand(AddIndex);
            DeleteIndexCommand = new RelayCommand<IndexDefinition>(DeleteIndex, CanDeleteIndex);
            EditIndexCommand = new RelayCommand<IndexDefinition>(EditIndex, CanEditIndex);

            AddConstraintCommand = new RelayCommand(AddConstraint);
            DeleteConstraintCommand = new RelayCommand<ConstraintDefinition>(DeleteConstraint, CanDeleteConstraint);
            EditConstraintCommand = new RelayCommand<ConstraintDefinition>(EditConstraint, CanEditConstraint);

            AddTriggerCommand = new RelayCommand(AddTrigger);
            DeleteTriggerCommand = new RelayCommand<TriggerDefinition>(DeleteTrigger, CanDeleteTrigger);
            EditTriggerCommand = new RelayCommand<TriggerDefinition>(EditTrigger, CanEditTrigger);

            CopyDdlCommand = new RelayCommand(CopyDdl, CanCopyDdl);
            SaveDdlCommand = new RelayCommand(SaveDdl, CanSaveDdl);
            ExecuteDdlCommand = new AsyncRelayCommand(ExecuteDdl, CanExecuteDdl);

            // 訂閱資料表定義變更事件
            _tableDefinition.PropertyChanged += (s, e) => OnTableDefinitionChanged();
        }

        /// <summary>
        /// 載入物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            TableDefinition = await _objectEditorService.GetTableDefinitionAsync(connection, ObjectName);

            // 訂閱資料表定義變更事件
            TableDefinition.PropertyChanged += (s, e) => OnTableDefinitionChanged();

            // 訂閱欄位集合變更事件
            TableDefinition.Columns.CollectionChanged += (s, e) => OnTableDefinitionChanged();

            // 訂閱索引集合變更事件
            TableDefinition.Indexes.CollectionChanged += (s, e) => OnTableDefinitionChanged();

            // 訂閱約束條件集合變更事件
            TableDefinition.Constraints.CollectionChanged += (s, e) => OnTableDefinitionChanged();

            // 訂閱觸發器集合變更事件
            TableDefinition.Triggers.CollectionChanged += (s, e) => OnTableDefinitionChanged();

            // 訂閱欄位屬性變更事件
            foreach (var column in TableDefinition.Columns)
            {
                column.PropertyChanged += (s, e) => OnTableDefinitionChanged();
            }

            // 產生 DDL 預覽
            await UpdateDdlPreviewAsync();
        }

        /// <summary>
        /// 儲存物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            await _objectEditorService.SaveTableDefinitionAsync(connection, TableDefinition);

            // 更新 DDL 預覽
            await UpdateDdlPreviewAsync();
        }

        /// <summary>
        /// 產生腳本
        /// </summary>
        /// <returns>腳本</returns>
        protected override string GenerateScript()
        {
            return DdlPreview;
        }

        /// <summary>
        /// 驗證物件
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            return TableDefinition.Validate();
        }

        /// <summary>
        /// 資料表定義變更處理
        /// </summary>
        private void OnTableDefinitionChanged()
        {
            if (_isInitializing || _disposed)
                return;

            HasUnsavedChanges = true;

            // 使用 Task.Run 避免在 UI 執行緒上執行非同步操作
            Task.Run(async () =>
            {
                try
                {
                    // 再次檢查是否已釋放，避免在非同步操作中繼續執行
                    if (_disposed)
                        return;

                    await UpdateDdlPreviewAsync();
                }
                catch (Exception ex)
                {
                    // 記錄錯誤但不拋出異常，避免程式當機
                    System.Diagnostics.Debug.WriteLine($"更新 DDL 預覽時發生錯誤: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 更新 DDL 預覽
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task UpdateDdlPreviewAsync()
        {
            try
            {
                var connection = _getConnection();
                if (connection == null)
                {
                    DdlPreview = "-- 無法取得資料庫連線";
                    return;
                }

                // 產生 DDL 腳本
                var script = await _scriptGeneratorService.GenerateCreateScriptAsync(connection, ObjectName, ObjectType);
                DdlPreview = script;
            }
            catch (Exception ex)
            {
                DdlPreview = $"-- 產生 DDL 預覽時發生錯誤：{ex.Message}";
            }
        }

        #region Column Operations

        /// <summary>
        /// 新增欄位
        /// </summary>
        private void AddColumn()
        {
            var newColumn = new ColumnDefinition
            {
                Name = $"COLUMN{TableDefinition.Columns.Count + 1}",
                DataType = "VARCHAR2",
                Length = 50,
                IsNullable = true
            };

            TableDefinition.Columns.Add(newColumn);
            SelectedColumn = newColumn;

            // 訂閱欄位屬性變更事件
            newColumn.PropertyChanged += (s, e) => OnTableDefinitionChanged();
        }

        /// <summary>
        /// 刪除欄位
        /// </summary>
        /// <param name="column">欄位</param>
        private void DeleteColumn(ColumnDefinition column)
        {
            if (column == null)
                return;

            // 檢查欄位是否被索引或約束條件使用
            var isUsedByIndex = TableDefinition.Indexes.Any(i => i.Columns.Any(c => c.ColumnName == column.Name));
            var isUsedByConstraint = TableDefinition.Constraints.Any(c => c.Columns.Contains(column.Name));

            if (isUsedByIndex || isUsedByConstraint)
            {
                var message = "此欄位正在被以下物件使用：\n";
                if (isUsedByIndex)
                {
                    message += "- 索引：" + string.Join(", ", TableDefinition.Indexes
                        .Where(i => i.Columns.Any(c => c.ColumnName == column.Name))
                        .Select(i => i.Name)) + "\n";
                }
                if (isUsedByConstraint)
                {
                    message += "- 約束條件：" + string.Join(", ", TableDefinition.Constraints
                        .Where(c => c.Columns.Contains(column.Name))
                        .Select(c => c.Name)) + "\n";
                }
                message += "\n是否仍要刪除此欄位？";

                var result = System.Windows.MessageBox.Show(
                    message,
                    "確認刪除",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning,
                    System.Windows.MessageBoxResult.No);

                if (result != System.Windows.MessageBoxResult.Yes)
                {
                    return;
                }
            }

            TableDefinition.Columns.Remove(column);
            if (SelectedColumn == column)
            {
                SelectedColumn = null;
            }
        }

        /// <summary>
        /// 是否可以刪除欄位
        /// </summary>
        /// <param name="column">欄位</param>
        /// <returns>是否可以刪除</returns>
        private bool CanDeleteColumn(ColumnDefinition column)
        {
            return column != null && TableDefinition.Columns.Count > 1;
        }

        /// <summary>
        /// 上移欄位
        /// </summary>
        /// <param name="column">欄位</param>
        private void MoveColumnUp(ColumnDefinition column)
        {
            if (column == null)
                return;

            var index = TableDefinition.Columns.IndexOf(column);
            if (index > 0)
            {
                TableDefinition.Columns.Move(index, index - 1);
            }
        }

        /// <summary>
        /// 是否可以上移欄位
        /// </summary>
        /// <param name="column">欄位</param>
        /// <returns>是否可以上移</returns>
        private bool CanMoveColumnUp(ColumnDefinition column)
        {
            if (column == null)
                return false;

            var index = TableDefinition.Columns.IndexOf(column);
            return index > 0;
        }

        /// <summary>
        /// 下移欄位
        /// </summary>
        /// <param name="column">欄位</param>
        private void MoveColumnDown(ColumnDefinition column)
        {
            if (column == null)
                return;

            var index = TableDefinition.Columns.IndexOf(column);
            if (index < TableDefinition.Columns.Count - 1)
            {
                TableDefinition.Columns.Move(index, index + 1);
            }
        }

        /// <summary>
        /// 是否可以下移欄位
        /// </summary>
        /// <param name="column">欄位</param>
        /// <returns>是否可以下移</returns>
        private bool CanMoveColumnDown(ColumnDefinition column)
        {
            if (column == null)
                return false;

            var index = TableDefinition.Columns.IndexOf(column);
            return index < TableDefinition.Columns.Count - 1;
        }

        #endregion

        #region Index Operations

        /// <summary>
        /// 新增索引
        /// </summary>
        private void AddIndex()
        {
            // 在實際應用中，這裡會開啟一個索引編輯對話框
            // 暫時使用簡單的實作
            var newIndex = new IndexDefinition
            {
                Name = $"{TableDefinition.Name}_IDX{TableDefinition.Indexes.Count + 1}",
                TableName = TableDefinition.Name,
                Type = IndexType.Normal,
                IsUnique = false
            };

            // 如果有選取的欄位，將其加入索引
            if (SelectedColumn != null)
            {
                newIndex.Columns.Add(new IndexColumnDefinition
                {
                    ColumnName = SelectedColumn.Name,
                    Position = 1,
                    IsDescending = false
                });
            }

            TableDefinition.Indexes.Add(newIndex);
            SelectedIndex = newIndex;
        }

        /// <summary>
        /// 刪除索引
        /// </summary>
        /// <param name="index">索引</param>
        private void DeleteIndex(IndexDefinition index)
        {
            if (index == null)
                return;

            TableDefinition.Indexes.Remove(index);
            if (SelectedIndex == index)
            {
                SelectedIndex = null;
            }
        }

        /// <summary>
        /// 是否可以刪除索引
        /// </summary>
        /// <param name="index">索引</param>
        /// <returns>是否可以刪除</returns>
        private bool CanDeleteIndex(IndexDefinition index)
        {
            return index != null;
        }

        /// <summary>
        /// 編輯索引
        /// </summary>
        /// <param name="index">索引</param>
        private void EditIndex(IndexDefinition index)
        {
            // 在實際應用中，這裡會開啟一個索引編輯對話框
            // 暫時使用簡單的實作
            SelectedIndex = index;
        }

        /// <summary>
        /// 是否可以編輯索引
        /// </summary>
        /// <param name="index">索引</param>
        /// <returns>是否可以編輯</returns>
        private bool CanEditIndex(IndexDefinition index)
        {
            return index != null;
        }

        #endregion

        #region Constraint Operations

        /// <summary>
        /// 新增約束條件
        /// </summary>
        private void AddConstraint()
        {
            // 在實際應用中，這裡會開啟一個約束條件編輯對話框
            // 暫時使用簡單的實作
            var newConstraint = new ConstraintDefinition
            {
                Name = $"{TableDefinition.Name}_CON{TableDefinition.Constraints.Count + 1}",
                TableName = TableDefinition.Name,
                Type = ConstraintType.Check,
                IsEnabled = true
            };

            // 如果有選取的欄位，將其加入約束條件
            if (SelectedColumn != null)
            {
                newConstraint.Columns.Add(SelectedColumn.Name);
                newConstraint.CheckCondition = $"{SelectedColumn.Name} IS NOT NULL";
            }

            TableDefinition.Constraints.Add(newConstraint);
            SelectedConstraint = newConstraint;
        }

        /// <summary>
        /// 刪除約束條件
        /// </summary>
        /// <param name="constraint">約束條件</param>
        private void DeleteConstraint(ConstraintDefinition constraint)
        {
            if (constraint == null)
                return;

            TableDefinition.Constraints.Remove(constraint);
            if (SelectedConstraint == constraint)
            {
                SelectedConstraint = null;
            }
        }

        /// <summary>
        /// 是否可以刪除約束條件
        /// </summary>
        /// <param name="constraint">約束條件</param>
        /// <returns>是否可以刪除</returns>
        private bool CanDeleteConstraint(ConstraintDefinition constraint)
        {
            return constraint != null;
        }

        /// <summary>
        /// 編輯約束條件
        /// </summary>
        /// <param name="constraint">約束條件</param>
        private void EditConstraint(ConstraintDefinition constraint)
        {
            // 在實際應用中，這裡會開啟一個約束條件編輯對話框
            // 暫時使用簡單的實作
            SelectedConstraint = constraint;
        }

        /// <summary>
        /// 是否可以編輯約束條件
        /// </summary>
        /// <param name="constraint">約束條件</param>
        /// <returns>是否可以編輯</returns>
        private bool CanEditConstraint(ConstraintDefinition constraint)
        {
            return constraint != null;
        }

        #endregion

        #region Trigger Operations

        /// <summary>
        /// 新增觸發器
        /// </summary>
        private void AddTrigger()
        {
            // 在實際應用中，這裡會開啟一個觸發器編輯對話框
            // 暫時使用簡單的實作
            var newTrigger = new TriggerDefinition
            {
                Name = $"{TableDefinition.Name}_TRG{TableDefinition.Triggers.Count + 1}",
                TableName = TableDefinition.Name,
                Type = TriggerType.Row,
                Event = TriggerEvent.Insert,
                Timing = TriggerTiming.Before,
                IsEnabled = true,
                Body = $"CREATE OR REPLACE TRIGGER {TableDefinition.Name}_TRG{TableDefinition.Triggers.Count + 1}\n" +
                       $"BEFORE INSERT ON {TableDefinition.Name}\n" +
                       "FOR EACH ROW\n" +
                       "BEGIN\n" +
                       "  -- 觸發器邏輯\n" +
                       "  NULL;\n" +
                       "END;"
            };

            TableDefinition.Triggers.Add(newTrigger);
            SelectedTrigger = newTrigger;
        }

        /// <summary>
        /// 刪除觸發器
        /// </summary>
        /// <param name="trigger">觸發器</param>
        private void DeleteTrigger(TriggerDefinition trigger)
        {
            if (trigger == null)
                return;

            TableDefinition.Triggers.Remove(trigger);
            if (SelectedTrigger == trigger)
            {
                SelectedTrigger = null;
            }
        }

        /// <summary>
        /// 是否可以刪除觸發器
        /// </summary>
        /// <param name="trigger">觸發器</param>
        /// <returns>是否可以刪除</returns>
        private bool CanDeleteTrigger(TriggerDefinition trigger)
        {
            return trigger != null;
        }

        /// <summary>
        /// 編輯觸發器
        /// </summary>
        /// <param name="trigger">觸發器</param>
        private void EditTrigger(TriggerDefinition trigger)
        {
            // 在實際應用中，這裡會開啟一個觸發器編輯對話框
            // 暫時使用簡單的實作
            SelectedTrigger = trigger;
        }

        /// <summary>
        /// 是否可以編輯觸發器
        /// </summary>
        /// <param name="trigger">觸發器</param>
        /// <returns>是否可以編輯</returns>
        private bool CanEditTrigger(TriggerDefinition trigger)
        {
            return trigger != null;
        }

        #endregion

        #region DDL Operations

        /// <summary>
        /// 複製 DDL
        /// </summary>
        private void CopyDdl()
        {
            try
            {
                System.Windows.Clipboard.SetText(DdlPreview);
                StatusMessage = "DDL 已複製到剪貼簿";
            }
            catch (Exception ex)
            {
                StatusMessage = $"複製 DDL 失敗：{ex.Message}";
            }
        }

        /// <summary>
        /// 是否可以複製 DDL
        /// </summary>
        /// <returns>是否可以複製</returns>
        private bool CanCopyDdl()
        {
            return !string.IsNullOrWhiteSpace(DdlPreview);
        }

        /// <summary>
        /// 儲存 DDL
        /// </summary>
        private void SaveDdl()
        {
            try
            {
                // 在實際應用中，這裡會開啟一個儲存檔案對話框
                // 暫時使用簡單的實作
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    FileName = $"{TableDefinition.Name}_DDL",
                    DefaultExt = ".sql",
                    Filter = "SQL 檔案 (*.sql)|*.sql|所有檔案 (*.*)|*.*"
                };

                if (dialog.ShowDialog() == true)
                {
                    System.IO.File.WriteAllText(dialog.FileName, DdlPreview);
                    StatusMessage = $"DDL 已儲存至 {dialog.FileName}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"儲存 DDL 失敗：{ex.Message}";
            }
        }

        /// <summary>
        /// 是否可以儲存 DDL
        /// </summary>
        /// <returns>是否可以儲存</returns>
        private bool CanSaveDdl()
        {
            return !string.IsNullOrWhiteSpace(DdlPreview);
        }

        /// <summary>
        /// 執行 DDL
        /// </summary>
        private async Task ExecuteDdl()
        {
            try
            {
                // 在實際應用中，這裡會執行 DDL 語句
                // 暫時使用簡單的實作
                var result = System.Windows.MessageBox.Show(
                    "確定要執行此 DDL 語句嗎？\n\n這可能會修改或重建資料表結構。",
                    "確認執行",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning,
                    System.Windows.MessageBoxResult.No);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    var connection = _getConnection();
                    if (connection == null)
                    {
                        StatusMessage = "無法取得資料庫連線";
                        return;
                    }

                    // 執行 DDL
                    await _databaseService.ExecuteNonQueryAsync(connection, DdlPreview);
                    StatusMessage = "DDL 已成功執行";
                    HasUnsavedChanges = false;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"執行 DDL 失敗：{ex.Message}";
            }
        }

        /// <summary>
        /// 是否可以執行 DDL
        /// </summary>
        /// <returns>是否可以執行</returns>
        private bool CanExecuteDdl()
        {
            return !string.IsNullOrWhiteSpace(DdlPreview) && _getConnection() != null;
        }

        #endregion

        #region 資源清理

        /// <summary>
        /// 在釋放資源時執行
        /// </summary>
        protected override void OnDisposing()
        {
            try
            {
                // 取消訂閱事件以避免記憶體洩漏和潛在的當機
                if (TableDefinition != null)
                {
                    TableDefinition.PropertyChanged -= (s, e) => OnTableDefinitionChanged();

                    if (TableDefinition.Columns != null)
                    {
                        TableDefinition.Columns.CollectionChanged -= (s, e) => OnTableDefinitionChanged();

                        // 取消訂閱每個欄位的屬性變更事件
                        foreach (var column in TableDefinition.Columns)
                        {
                            if (column != null)
                            {
                                column.PropertyChanged -= (s, e) => OnTableDefinitionChanged();
                            }
                        }
                    }

                    if (TableDefinition.Indexes != null)
                    {
                        TableDefinition.Indexes.CollectionChanged -= (s, e) => OnTableDefinitionChanged();
                    }

                    if (TableDefinition.Constraints != null)
                    {
                        TableDefinition.Constraints.CollectionChanged -= (s, e) => OnTableDefinitionChanged();
                    }

                    if (TableDefinition.Triggers != null)
                    {
                        TableDefinition.Triggers.CollectionChanged -= (s, e) => OnTableDefinitionChanged();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理 TableEditorViewModel 資源時發生錯誤: {ex.Message}");
            }

            base.OnDisposing();
        }

        #endregion
    }
}