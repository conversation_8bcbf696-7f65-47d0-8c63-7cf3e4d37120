using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using OracleMS.Models;

namespace OracleMS.Views;

/// <summary>
/// Converts connection status to color for visual indication
/// </summary>
public class ConnectionStatusConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DateTime lastConnected)
        {
            var timeSinceLastConnection = DateTime.Now - lastConnected;
            
            // Green if connected within last hour
            if (timeSinceLastConnection.TotalHours < 1)
                return new SolidColorBrush(Colors.Green);
            
            // Yellow if connected within last day
            if (timeSinceLastConnection.TotalDays < 1)
                return new SolidColorBrush(Colors.Orange);
            
            // Red if not connected recently or never connected
            return new SolidColorBrush(Colors.Red);
        }
        
        // Default to red for never connected
        return new SolidColorBrush(Colors.Red);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Inverts boolean values
/// </summary>
public class InverseBooleanConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
            return !boolValue;
        
        return true;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
            return !boolValue;
        
        return false;
    }
}

/// <summary>
/// Converts string to visibility (empty/null = Collapsed, otherwise Visible)
/// </summary>
public class StringToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return string.IsNullOrEmpty(value?.ToString()) ? Visibility.Collapsed : Visibility.Visible;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converts boolean to visibility with option to invert
/// </summary>
public class BooleanToVisibilityConverter : IValueConverter
{
    public bool Invert { get; set; } = false;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        try
        {
            // 檢查是否為 DependencyProperty.UnsetValue 或 null
            if (value == DependencyProperty.UnsetValue || value == null)
            {
                return Invert ? Visibility.Visible : Visibility.Collapsed;
            }

            if (value is bool boolValue)
            {
                var result = Invert ? !boolValue : boolValue;
                return result ? Visibility.Visible : Visibility.Collapsed;
            }

            // 如果不是 bool 類型，嘗試轉換
            if (value != null)
            {
                var stringValue = value.ToString();
                if (!string.IsNullOrEmpty(stringValue) && bool.TryParse(stringValue, out bool parsedValue))
                {
                    var result = Invert ? !parsedValue : parsedValue;
                    return result ? Visibility.Visible : Visibility.Collapsed;
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"BooleanToVisibilityConverter 轉換錯誤: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"Value: {value}, Type: {value?.GetType().Name}");
        }

        // 預設行為：如果無法轉換，根據 Invert 設定返回適當的值
        return Invert ? Visibility.Visible : Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        try
        {
            if (value is Visibility visibility)
            {
                var result = visibility == Visibility.Visible;
                return Invert ? !result : result;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"BooleanToVisibilityConverter 反向轉換錯誤: {ex.Message}");
        }

        return false;
    }
}

/// <summary>
/// 更安全的 Boolean 到 Visibility 轉換器，專門處理可能的綁定錯誤
/// </summary>
public class SafeBooleanToVisibilityConverter : IValueConverter
{
    public bool Invert { get; set; } = false;
    public Visibility DefaultVisibility { get; set; } = Visibility.Collapsed;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        try
        {
            // 處理各種可能的無效值
            if (value == null ||
                value == DependencyProperty.UnsetValue ||
                value == Binding.DoNothing)
            {
                return DefaultVisibility;
            }

            // 嘗試轉換為 bool
            bool boolValue = false;

            if (value is bool directBool)
            {
                boolValue = directBool;
            }
            else if (value is string stringValue)
            {
                if (!bool.TryParse(stringValue, out boolValue))
                {
                    return DefaultVisibility;
                }
            }
            else
            {
                // 嘗試其他類型的轉換
                try
                {
                    boolValue = System.Convert.ToBoolean(value);
                }
                catch
                {
                    return DefaultVisibility;
                }
            }

            var result = Invert ? !boolValue : boolValue;
            return result ? Visibility.Visible : Visibility.Collapsed;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SafeBooleanToVisibilityConverter 錯誤: {ex.Message}");
            return DefaultVisibility;
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        try
        {
            if (value is Visibility visibility)
            {
                var result = visibility == Visibility.Visible;
                return Invert ? !result : result;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SafeBooleanToVisibilityConverter 反向轉換錯誤: {ex.Message}");
        }

        return false;
    }
}

/// <summary>
/// Converts DatabaseObjectType to appropriate icon
/// </summary>
public class DatabaseObjectTypeToIconConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DatabaseObjectType objectType)
        {
            return objectType switch
            {
                DatabaseObjectType.Table => "📋",
                DatabaseObjectType.View => "👁️",
                DatabaseObjectType.Procedure => "⚙️",
                DatabaseObjectType.Function => "🔧",
                DatabaseObjectType.Package => "📦",
                DatabaseObjectType.Sequence => "🔢",
                DatabaseObjectType.Trigger => "⚡",
                DatabaseObjectType.Index => "🗂️",
                _ => "📄"
            };
        }
        
        return "📄";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converts inverted boolean to visibility
/// </summary>
public class InverseBooleanToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return !boolValue ? Visibility.Visible : Visibility.Collapsed;
        }
        
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
        {
            return visibility != Visibility.Visible;
        }
        
        return true;
    }
}

/// <summary>
/// Converts TimeSpan to visibility (zero = Collapsed, otherwise Visible)
/// </summary>
public class TimeSpanToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is TimeSpan timeSpan)
        {
            return timeSpan.TotalSeconds > 0 ? Visibility.Visible : Visibility.Collapsed;
        }
        
        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converts index to 1-based number for display
/// </summary>
public class IndexToNumberConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int index)
        {
            return (index + 1).ToString();
        }
        
        return "1";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (int.TryParse(value?.ToString(), out int number))
        {
            return number - 1;
        }
        
        return 0;
    }
}

/// <summary>
/// Converts item and collection to 1-based index for display
/// </summary>
public class ItemIndexConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length >= 2 && values[0] != null && values[1] is System.Collections.IList collection)
        {
            var index = collection.IndexOf(values[0]);
            return (index + 1).ToString();
        }
        
        return "1";
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converts string to integer and vice versa
/// </summary>
public class StringToIntConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int intValue)
        {
            return intValue.ToString();
        }

        return "100";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (int.TryParse(value?.ToString(), out int result))
        {
            return result;
        }

        return 100;
    }
}

/// <summary>
/// Converts boolean to modified/unmodified text
/// </summary>
public class BoolToModifiedConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool hasUnsavedChanges)
        {
            return hasUnsavedChanges ? "已修改" : "未修改";
        }

        return "未修改";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Static converter instances for use in XAML
/// </summary>
public static class Converters
{
    public static readonly InverseBooleanConverter InverseBooleanConverter = new();
    public static readonly BooleanToVisibilityConverter BooleanToVisibilityConverter = new();
    public static readonly InverseBooleanToVisibilityConverter InverseBooleanToVisibilityConverter = new();
    public static readonly TimeSpanToVisibilityConverter TimeSpanToVisibilityConverter = new();
    public static readonly IndexToNumberConverter IndexToNumberConverter = new();
    public static readonly ItemIndexConverter ItemIndexConverter = new();
    public static readonly StringToIntConverter StringToIntConverter = new();
    public static readonly BoolToModifiedConverter BoolToModifiedConverter = new();
}