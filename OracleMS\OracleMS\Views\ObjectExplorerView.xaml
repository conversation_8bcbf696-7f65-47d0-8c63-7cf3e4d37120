<UserControl x:Class="OracleMS.Views.ObjectExplorerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:vm="clr-namespace:OracleMS.ViewModels"
             xmlns:models="clr-namespace:OracleMS.Models"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="300">
    
    <UserControl.Resources>
        <!-- Database Object Type to Icon Converter -->
        <local:DatabaseObjectTypeToIconConverter x:Key="DatabaseObjectTypeToIconConverter"/>
        
        <!-- Boolean to Visibility Converter -->
        <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Safe Boolean to Visibility Converter -->
        <local:SafeBooleanToVisibilityConverter x:Key="SafeBooleanToVisibilityConverter"/>

        <!-- Inverse Boolean to Visibility Converter -->
        <local:BooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" Invert="True"/>

        <!-- String to Visibility Converter -->
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        
        <!-- TreeView Item Template for Database Objects -->
        <HierarchicalDataTemplate x:Key="DatabaseObjectTemplate" 
                                  DataType="{x:Type vm:DatabaseObjectNode}"
                                  ItemsSource="{Binding Children}">
            <StackPanel Orientation="Horizontal">
                <StackPanel.Style>
                    <Style TargetType="StackPanel">
                        <Setter Property="Visibility" Value="Visible"/>
                        <!-- 隱藏虛擬項目 -->
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsPlaceholder}" Value="True">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsVisible}" Value="False">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </StackPanel.Style>

                <!-- Object Icon -->
                <TextBlock Text="{Binding ObjectType, Converter={StaticResource DatabaseObjectTypeToIconConverter}}"
                           FontSize="14" Margin="0,0,6,0" VerticalAlignment="Center"/>

                <!-- Object Name -->
                <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center" FontSize="12"/>
            </StackPanel>
        </HierarchicalDataTemplate>
        
        <!-- TreeView Item Container Style with Virtualization Support and Event Handlers -->
        <Style x:Key="TreeViewItemStyle" TargetType="TreeViewItem">
            <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
            <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Margin" Value="0,1"/>
            <Setter Property="Padding" Value="2"/>
            <!-- Enable virtualization for better performance -->
            <Setter Property="VirtualizingPanel.IsVirtualizing" Value="True"/>
            <Setter Property="VirtualizingPanel.VirtualizationMode" Value="Recycling"/>
            <!-- 設定一個空的 ContextMenu，內容將在事件處理中動態創建 -->
            <Setter Property="ContextMenu">
                <Setter.Value>
                    <ContextMenu/>
                </Setter.Value>
            </Setter>
            <!-- Event Handlers -->
            <EventSetter Event="MouseDoubleClick" Handler="OnTreeViewItemDoubleClick"/>
            <EventSetter Event="Expanded" Handler="OnTreeViewItemExpanded"/>
            <EventSetter Event="ContextMenuOpening" Handler="OnTreeViewItemContextMenuOpening"/>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="BorderThickness" Value="1"/>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F5F5F5"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsFolder}" Value="True">
                    <Setter Property="FontWeight" Value="SemiBold"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
        
        <!-- Context Menu for Database Objects -->
        <ContextMenu x:Key="DatabaseObjectContextMenu">
            <!-- Table-specific menu items -->
            <MenuItem Header="查看資料" Command="{Binding DataContext.OpenTableDataCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <TextBlock Text="📊" FontSize="12"/>
                </MenuItem.Icon>
                <MenuItem.Style>
                    <Style TargetType="MenuItem">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsTableObject, FallbackValue=False}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </MenuItem.Style>
            </MenuItem>

            <MenuItem Header="設計資料表" Command="{Binding DataContext.OpenTableDesignCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <TextBlock Text="🔧" FontSize="12"/>
                </MenuItem.Icon>
                <MenuItem.Style>
                    <Style TargetType="MenuItem">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsTableObject, FallbackValue=False}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </MenuItem.Style>
            </MenuItem>

            <Separator>
                <Separator.Style>
                    <Style TargetType="Separator">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsTableObject, FallbackValue=False}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Separator.Style>
            </Separator>

            <!-- Script generation menu items -->
            <MenuItem Header="產生腳本">
                <MenuItem.Icon>
                    <TextBlock Text="📝" FontSize="12"/>
                </MenuItem.Icon>
                <MenuItem.Style>
                    <Style TargetType="MenuItem">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsPlaceholder, FallbackValue=True}" Value="False">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </MenuItem.Style>

                <MenuItem Header="CREATE 腳本" Command="{Binding DataContext.GenerateCreateScriptCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          CommandParameter="{Binding}"/>

                <MenuItem Header="INSERT 腳本" Command="{Binding DataContext.GenerateInsertScriptCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          CommandParameter="{Binding}">
                    <MenuItem.Style>
                        <Style TargetType="MenuItem">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsTableObject, FallbackValue=False}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </MenuItem.Style>
                </MenuItem>

                <MenuItem Header="DROP 腳本" Command="{Binding DataContext.GenerateDropScriptCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          CommandParameter="{Binding}"/>
            </MenuItem>

            <Separator/>

            <!-- Common menu items -->
            <MenuItem Header="複製物件名稱" Command="{Binding DataContext.CopyObjectNameCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <TextBlock Text="📋" FontSize="12"/>
                </MenuItem.Icon>
                <MenuItem.Style>
                    <Style TargetType="MenuItem">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsPlaceholder, FallbackValue=True}" Value="False">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </MenuItem.Style>
            </MenuItem>

            <MenuItem Header="重新整理" Command="{Binding DataContext.RefreshCommand, RelativeSource={RelativeSource AncestorType=UserControl}}">
                <MenuItem.Icon>
                    <TextBlock Text="🔄" FontSize="12"/>
                </MenuItem.Icon>
            </MenuItem>
        </ContextMenu>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Toolbar -->
        <ToolBar Grid.Row="0" Background="#F8F8F8" ToolBarTray.IsLocked="True">
            <Button Command="{Binding RefreshCommand}" ToolTip="重新整理資料庫物件">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔄" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="重新整理"/>
                </StackPanel>
            </Button>
            
            <Separator/>
            
            <Button Command="{Binding ExpandAllCommand}" ToolTip="展開所有節點">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📂" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="展開全部"/>
                </StackPanel>
            </Button>
            
            <Button Command="{Binding CollapseAllCommand}" ToolTip="摺疊所有節點">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📁" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="摺疊全部"/>
                </StackPanel>
            </Button>
        </ToolBar>
        
        <!-- Search Box -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,1" Padding="8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Search Icon -->
                <TextBlock Grid.Column="0" Text="🔍" FontSize="14" VerticalAlignment="Center" Margin="0,0,6,0"/>
                
                <!-- Search TextBox -->
                <TextBox Grid.Column="1" 
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         VerticalAlignment="Center"
                         BorderThickness="0"
                         Background="Transparent">
                    <TextBox.Style>
                        <Style TargetType="TextBox">
                            <Style.Triggers>
                                <Trigger Property="Text" Value="">
                                    <Setter Property="Background">
                                        <Setter.Value>
                                            <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                <VisualBrush.Visual>
                                                    <TextBlock Text="搜尋資料庫物件..." Foreground="Gray" FontStyle="Italic"/>
                                                </VisualBrush.Visual>
                                            </VisualBrush>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>
                
                <!-- Clear Search Button -->
                <Button Grid.Column="2" 
                        Content="✖" 
                        FontSize="10"
                        Width="16" Height="16"
                        Background="Transparent"
                        BorderThickness="0"
                        Command="{Binding ClearSearchCommand}"
                        ToolTip="清除搜尋"
                        Visibility="{Binding SearchText, Converter={StaticResource StringToVisibilityConverter}}"/>
            </Grid>
        </Border>
        
        <!-- Object Tree with Virtualization for Performance -->
        <TreeView Grid.Row="2"
                  ItemsSource="{Binding RootNodes}"
                  ItemTemplate="{StaticResource DatabaseObjectTemplate}"
                  ItemContainerStyle="{StaticResource TreeViewItemStyle}"
                  SelectedItemChanged="OnTreeViewSelectedItemChanged"
                  Background="#FAFAFA"
                  BorderThickness="0"
                  VirtualizingPanel.IsVirtualizing="True"
                  VirtualizingPanel.VirtualizationMode="Recycling"
                  VirtualizingPanel.IsContainerVirtualizable="True"
                  ScrollViewer.CanContentScroll="True">
            
            <!-- TreeView Context Menu -->
            <TreeView.ContextMenu>
                <StaticResource ResourceKey="DatabaseObjectContextMenu"/>
            </TreeView.ContextMenu>
        </TreeView>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="3" Background="#F0F0F0">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <!-- Loading Indicator -->
                    <ProgressBar Width="16" Height="16" IsIndeterminate="True" 
                                 Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                 Margin="0,0,8,0"/>
                    
                    <!-- Status Text -->
                    <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                </StackPanel>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <Run Text="已選取: "/>
                    <Run Text="{Binding SelectedNode.DisplayName, FallbackValue='無'}"/>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>