using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using OracleMS.ViewModels;
using CommunityToolkit.Mvvm.Input;

namespace OracleMS.Views;

/// <summary>
/// ObjectExplorerView.xaml 的互動邏輯
/// </summary>
public partial class ObjectExplorerView : UserControl
{
    public ObjectExplorerView()
    {
        InitializeComponent();

        // 訂閱 ContextMenuOpening 事件以安全處理右鍵選單
        ContextMenuOpening += OnContextMenuOpening;
    }

    /// <summary>
    /// 處理 ContextMenu 開啟事件，確保只有有效的節點才顯示選單
    /// </summary>
    private void OnContextMenuOpening(object sender, ContextMenuEventArgs e)
    {
        try
        {
            // 檢查是否是有效的 TreeViewItem
            if (e.OriginalSource is FrameworkElement element)
            {
                var treeViewItem = FindParent<TreeViewItem>(element);
                if (treeViewItem?.DataContext is DatabaseObjectNode node)
                {
                    // 如果是 placeholder 節點，取消顯示 ContextMenu
                    if (node.IsPlaceholder)
                    {
                        e.Handled = true;
                        return;
                    }
                }
                else
                {
                    // 如果不是有效的節點，取消顯示 ContextMenu
                    e.Handled = true;
                    return;
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"ContextMenu 開啟時發生錯誤: {ex.Message}");
            e.Handled = true;
        }
    }


    /// <summary>
    /// Handle TreeView selected item changed event
    /// </summary>
    private void OnTreeViewSelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (DataContext is ObjectExplorerViewModel viewModel && e.NewValue is DatabaseObjectNode node)
        {
            viewModel.SelectedNode = node;
        }
    }

    /// <summary>
    /// Handle TreeView item double click event
    /// </summary>
    private void OnTreeViewItemDoubleClick(object sender, MouseButtonEventArgs e)
    {
        // 立即標記事件為已處理，防止冒泡
        e.Handled = true;

        if (e.OriginalSource is FrameworkElement element)
        {
            // 找到對應的 TreeViewItem
            var treeViewItem = FindParent<TreeViewItem>(element);
            if (treeViewItem?.DataContext is DatabaseObjectNode node)
            {
                if (DataContext is ObjectExplorerViewModel viewModel)
                {
                    // Double click on database object opens appropriate editor
                    if (!node.IsFolder)
                    {
                        // 保存當前展開狀態
                        var expandedStates = SaveExpandedStates();

                        switch (node.ObjectType)
                        {
                            case Models.DatabaseObjectType.Table:
                                // Double click on table opens table design
                                if (viewModel.OpenTableDesignCommand.CanExecute(node))
                                {
                                    viewModel.OpenTableDesignCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.View:
                                // Double click on view opens view editor
                                if (viewModel.OpenViewEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenViewEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Procedure:
                                // Double click on procedure opens procedure editor
                                if (viewModel.OpenProcedureEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenProcedureEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Function:
                                // Double click on function opens function editor
                                if (viewModel.OpenFunctionEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenFunctionEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Package:
                                // Double click on package opens package editor
                                if (viewModel.OpenPackageEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenPackageEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Sequence:
                                // Double click on sequence opens sequence editor
                                if (viewModel.OpenSequenceEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenSequenceEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Trigger:
                                // Double click on trigger opens trigger editor
                                if (viewModel.OpenTriggerEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenTriggerEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Index:
                                // Double click on index opens index editor
                                if (viewModel.OpenIndexEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenIndexEditorCommand.Execute(node);
                                }
                                break;
                        }

                        // 延遲恢復展開狀態，確保 UI 更新完成
                        Dispatcher.BeginInvoke(new Action(() => RestoreExpandedStates(expandedStates)),
                            System.Windows.Threading.DispatcherPriority.Background);
                    }
                    // Double click on folder expands/collapses it
                    else if (node.IsFolder)
                    {
                        node.IsExpanded = !node.IsExpanded;
                        if (node.IsExpanded && (!node.IsLoaded || HasOnlyPlaceholder(node)))
                        {
                            if (viewModel.ExpandNodeCommand.CanExecute(node))
                            {
                                // Execute the async command properly
                                if (viewModel.ExpandNodeCommand is AsyncRelayCommand<DatabaseObjectNode> asyncCommand)
                                {
                                    _ = asyncCommand.ExecuteAsync(node);
                                }
                                else
                                {
                                    viewModel.ExpandNodeCommand.Execute(node);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// Handle TreeView item expanded event
    /// </summary>
    private void OnTreeViewItemExpanded(object sender, RoutedEventArgs e)
    {
        System.Diagnostics.Debug.WriteLine($"OnTreeViewItemExpanded called - Sender: {sender?.GetType().Name}");

        if (e.Source is TreeViewItem item && item.DataContext is DatabaseObjectNode node)
        {
            System.Diagnostics.Debug.WriteLine($"TreeViewItem expanded - Node: {node.DisplayName}, IsFolder: {node.IsFolder}, IsLoaded: {node.IsLoaded}");

            if (DataContext is ObjectExplorerViewModel viewModel)
            {
                // Load children if it's a folder and either not loaded or only has placeholder
                if (node.IsFolder && (!node.IsLoaded || HasOnlyPlaceholder(node)))
                {
                    System.Diagnostics.Debug.WriteLine($"Attempting to load node: {node.DisplayName}");

                    if (viewModel.ExpandNodeCommand.CanExecute(node))
                    {
                        System.Diagnostics.Debug.WriteLine($"Command can execute, executing for node: {node.DisplayName}");

                        // Execute the async command properly
                        if (viewModel.ExpandNodeCommand is AsyncRelayCommand<DatabaseObjectNode> asyncCommand)
                        {
                            _ = asyncCommand.ExecuteAsync(node);
                        }
                        else
                        {
                            viewModel.ExpandNodeCommand.Execute(node);
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"Command cannot execute for node: {node.DisplayName}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Node {node.DisplayName} - IsFolder: {node.IsFolder}, IsLoaded: {node.IsLoaded}, HasOnlyPlaceholder: {HasOnlyPlaceholder(node)}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("DataContext is not ObjectExplorerViewModel");
            }
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"Event source is not TreeViewItem or DataContext is not DatabaseObjectNode - Source: {e.Source?.GetType().Name}");
        }
    }

    /// <summary>
    /// Check if node has only placeholder children
    /// </summary>
    private bool HasOnlyPlaceholder(DatabaseObjectNode node)
    {
        return node.Children.Count == 1 && node.Children[0].IsPlaceholder;
    }

    /// <summary>
    /// Handle keyboard shortcuts
    /// </summary>
    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (DataContext is ObjectExplorerViewModel viewModel)
        {
            switch (e.Key)
            {
                case Key.F5:
                    // F5 to refresh
                    if (viewModel.RefreshCommand.CanExecute(null))
                    {
                        viewModel.RefreshCommand.Execute(null);
                        e.Handled = true;
                    }
                    break;
                    
                case Key.Enter:
                    // Enter to open table data or expand folder
                    if (viewModel.SelectedNode != null)
                    {
                        if (viewModel.SelectedNode.IsTableObject)
                        {
                            if (viewModel.OpenTableDataCommand.CanExecute(viewModel.SelectedNode))
                            {
                                viewModel.OpenTableDataCommand.Execute(viewModel.SelectedNode);
                                e.Handled = true;
                            }
                        }
                        else if (viewModel.SelectedNode.IsFolder)
                        {
                            viewModel.SelectedNode.IsExpanded = !viewModel.SelectedNode.IsExpanded;
                            e.Handled = true;
                        }
                    }
                    break;
                    
                case Key.Delete:
                    // Delete key could be used for object operations in the future
                    break;
            }
        }

        base.OnKeyDown(e);
    }

    /// <summary>
    /// 找到指定類型的父控制項
    /// </summary>
    private static T? FindParent<T>(DependencyObject child) where T : DependencyObject
    {
        var parentObject = VisualTreeHelper.GetParent(child);

        if (parentObject == null) return null;

        if (parentObject is T parent)
            return parent;

        return FindParent<T>(parentObject);
    }

    /// <summary>
    /// 保存當前的展開狀態
    /// </summary>
    private Dictionary<string, bool> SaveExpandedStates()
    {
        var states = new Dictionary<string, bool>();
        if (DataContext is ObjectExplorerViewModel viewModel)
        {
            SaveNodeExpandedStates(viewModel.RootNodes, states);
        }
        return states;
    }

    /// <summary>
    /// 遞歸保存節點展開狀態
    /// </summary>
    private void SaveNodeExpandedStates(IEnumerable<DatabaseObjectNode> nodes, Dictionary<string, bool> states)
    {
        foreach (var node in nodes)
        {
            var key = $"{node.ObjectType}_{node.DisplayName}";
            states[key] = node.IsExpanded;

            if (node.Children.Any())
            {
                SaveNodeExpandedStates(node.Children, states);
            }
        }
    }

    /// <summary>
    /// 恢復展開狀態
    /// </summary>
    private void RestoreExpandedStates(Dictionary<string, bool> states)
    {
        if (DataContext is ObjectExplorerViewModel viewModel)
        {
            RestoreNodeExpandedStates(viewModel.RootNodes, states);
        }
    }

    /// <summary>
    /// 遞歸恢復節點展開狀態
    /// </summary>
    private void RestoreNodeExpandedStates(IEnumerable<DatabaseObjectNode> nodes, Dictionary<string, bool> states)
    {
        foreach (var node in nodes)
        {
            var key = $"{node.ObjectType}_{node.DisplayName}";
            if (states.TryGetValue(key, out var isExpanded))
            {
                node.IsExpanded = isExpanded;
            }

            if (node.Children.Any())
            {
                RestoreNodeExpandedStates(node.Children, states);
            }
        }
    }
}