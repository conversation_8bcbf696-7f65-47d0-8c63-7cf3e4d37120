using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using OracleMS.ViewModels;
using CommunityToolkit.Mvvm.Input;

namespace OracleMS.Views;

/// <summary>
/// ObjectExplorerView.xaml 的互動邏輯
/// </summary>
public partial class ObjectExplorerView : UserControl
{
    public ObjectExplorerView()
    {
        InitializeComponent();

        // 訂閱 ContextMenuOpening 事件以安全處理右鍵選單
        ContextMenuOpening += OnTreeViewItemContextMenuOpening;
    }

    /// <summary>
    /// 處理 TreeViewItem 的 ContextMenu 開啟事件
    /// </summary>
    private void OnTreeViewItemContextMenuOpening(object sender, ContextMenuEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("OnTreeViewItemContextMenuOpening called");

            if (sender is TreeViewItem treeViewItem)
            {
                if (treeViewItem.DataContext is DatabaseObjectNode node)
                {
                    System.Diagnostics.Debug.WriteLine($"Node: {node.DisplayName}, IsPlaceholder: {node.IsPlaceholder}, IsFolder: {node.IsFolder}");

                    // 如果是 placeholder 節點，取消顯示 ContextMenu
                    if (node.IsPlaceholder)
                    {
                        System.Diagnostics.Debug.WriteLine("Cancelling context menu for placeholder node");
                        e.Handled = true;
                        return;
                    }

                    // 清空現有的 ContextMenu 項目
                    if (treeViewItem.ContextMenu != null)
                    {
                        treeViewItem.ContextMenu.Items.Clear();

                        // 動態添加選單項目
                        PopulateContextMenu(treeViewItem.ContextMenu, node);

                        System.Diagnostics.Debug.WriteLine($"ContextMenu populated with {treeViewItem.ContextMenu.Items.Count} items");

                        // 如果沒有任何項目，取消顯示
                        if (treeViewItem.ContextMenu.Items.Count == 0)
                        {
                            System.Diagnostics.Debug.WriteLine("No menu items added, cancelling");
                            e.Handled = true;
                            return;
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("ContextMenu is null, cancelling");
                        e.Handled = true;
                        return;
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("DataContext is not DatabaseObjectNode, cancelling");
                    e.Handled = true;
                    return;
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("Sender is not TreeViewItem, cancelling");
                e.Handled = true;
                return;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"ContextMenu 開啟時發生錯誤: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"StackTrace: {ex.StackTrace}");
            e.Handled = true;
        }
    }

    /// <summary>
    /// 為指定節點填充 ContextMenu 項目
    /// </summary>
    private void PopulateContextMenu(ContextMenu contextMenu, DatabaseObjectNode node)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"PopulateContextMenu called for node: {node.DisplayName}");

            // 如果是資料表物件，添加資料表特定的選單項目
            if (node.IsTableObject)
            {
                System.Diagnostics.Debug.WriteLine("Adding table-specific menu items");

                var viewDataItem = new MenuItem
                {
                    Header = "查看資料",
                    Icon = new TextBlock { Text = "📊", FontSize = 12 }
                };
                viewDataItem.Click += (s, e) => OnViewTableData(node);
                contextMenu.Items.Add(viewDataItem);

                var designTableItem = new MenuItem
                {
                    Header = "設計資料表",
                    Icon = new TextBlock { Text = "🔧", FontSize = 12 }
                };
                designTableItem.Click += (s, e) => OnDesignTable(node);
                contextMenu.Items.Add(designTableItem);

                contextMenu.Items.Add(new Separator());
            }

            // 如果不是 placeholder，添加腳本生成選單
            if (!node.IsPlaceholder)
            {
                System.Diagnostics.Debug.WriteLine("Adding script generation menu items");

                var scriptMenu = new MenuItem
                {
                    Header = "產生腳本",
                    Icon = new TextBlock { Text = "📝", FontSize = 12 }
                };

                var createScriptItem = new MenuItem { Header = "CREATE 腳本" };
                createScriptItem.Click += (s, e) => OnGenerateCreateScript(node);
                scriptMenu.Items.Add(createScriptItem);

                if (node.IsTableObject)
                {
                    var insertScriptItem = new MenuItem { Header = "INSERT 腳本" };
                    insertScriptItem.Click += (s, e) => OnGenerateInsertScript(node);
                    scriptMenu.Items.Add(insertScriptItem);
                }

                var dropScriptItem = new MenuItem { Header = "DROP 腳本" };
                dropScriptItem.Click += (s, e) => OnGenerateDropScript(node);
                scriptMenu.Items.Add(dropScriptItem);

                contextMenu.Items.Add(scriptMenu);
                contextMenu.Items.Add(new Separator());

                var copyNameItem = new MenuItem
                {
                    Header = "複製物件名稱",
                    Icon = new TextBlock { Text = "📋", FontSize = 12 }
                };
                copyNameItem.Click += (s, e) => OnCopyObjectName(node);
                contextMenu.Items.Add(copyNameItem);
            }

            // 重新整理選項（所有節點都有）
            System.Diagnostics.Debug.WriteLine("Adding refresh menu item");
            var refreshItem = new MenuItem
            {
                Header = "重新整理",
                Icon = new TextBlock { Text = "🔄", FontSize = 12 }
            };
            refreshItem.Click += (s, e) => OnRefresh();
            contextMenu.Items.Add(refreshItem);

            System.Diagnostics.Debug.WriteLine($"Total menu items added: {contextMenu.Items.Count}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"填充 ContextMenu 時發生錯誤: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"StackTrace: {ex.StackTrace}");
        }
    }


    /// <summary>
    /// Handle TreeView selected item changed event
    /// </summary>
    private void OnTreeViewSelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (DataContext is ObjectExplorerViewModel viewModel && e.NewValue is DatabaseObjectNode node)
        {
            viewModel.SelectedNode = node;
        }
    }

    /// <summary>
    /// Handle TreeView item double click event
    /// </summary>
    private void OnTreeViewItemDoubleClick(object sender, MouseButtonEventArgs e)
    {
        // 立即標記事件為已處理，防止冒泡
        e.Handled = true;

        if (e.OriginalSource is FrameworkElement element)
        {
            // 找到對應的 TreeViewItem
            var treeViewItem = FindParent<TreeViewItem>(element);
            if (treeViewItem?.DataContext is DatabaseObjectNode node)
            {
                if (DataContext is ObjectExplorerViewModel viewModel)
                {
                    // Double click on database object opens appropriate editor
                    if (!node.IsFolder)
                    {
                        // 保存當前展開狀態
                        var expandedStates = SaveExpandedStates();

                        switch (node.ObjectType)
                        {
                            case Models.DatabaseObjectType.Table:
                                // Double click on table opens table design
                                if (viewModel.OpenTableDesignCommand.CanExecute(node))
                                {
                                    viewModel.OpenTableDesignCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.View:
                                // Double click on view opens view editor
                                if (viewModel.OpenViewEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenViewEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Procedure:
                                // Double click on procedure opens procedure editor
                                if (viewModel.OpenProcedureEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenProcedureEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Function:
                                // Double click on function opens function editor
                                if (viewModel.OpenFunctionEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenFunctionEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Package:
                                // Double click on package opens package editor
                                if (viewModel.OpenPackageEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenPackageEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Sequence:
                                // Double click on sequence opens sequence editor
                                if (viewModel.OpenSequenceEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenSequenceEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Trigger:
                                // Double click on trigger opens trigger editor
                                if (viewModel.OpenTriggerEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenTriggerEditorCommand.Execute(node);
                                }
                                break;

                            case Models.DatabaseObjectType.Index:
                                // Double click on index opens index editor
                                if (viewModel.OpenIndexEditorCommand?.CanExecute(node) == true)
                                {
                                    viewModel.OpenIndexEditorCommand.Execute(node);
                                }
                                break;
                        }

                        // 延遲恢復展開狀態，確保 UI 更新完成
                        Dispatcher.BeginInvoke(new Action(() => RestoreExpandedStates(expandedStates)),
                            System.Windows.Threading.DispatcherPriority.Background);
                    }
                    // Double click on folder expands/collapses it
                    else if (node.IsFolder)
                    {
                        node.IsExpanded = !node.IsExpanded;
                        if (node.IsExpanded && (!node.IsLoaded || HasOnlyPlaceholder(node)))
                        {
                            if (viewModel.ExpandNodeCommand.CanExecute(node))
                            {
                                // Execute the async command properly
                                if (viewModel.ExpandNodeCommand is AsyncRelayCommand<DatabaseObjectNode> asyncCommand)
                                {
                                    _ = asyncCommand.ExecuteAsync(node);
                                }
                                else
                                {
                                    viewModel.ExpandNodeCommand.Execute(node);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// Handle TreeView item expanded event
    /// </summary>
    private void OnTreeViewItemExpanded(object sender, RoutedEventArgs e)
    {
        System.Diagnostics.Debug.WriteLine($"OnTreeViewItemExpanded called - Sender: {sender?.GetType().Name}");

        if (e.Source is TreeViewItem item && item.DataContext is DatabaseObjectNode node)
        {
            System.Diagnostics.Debug.WriteLine($"TreeViewItem expanded - Node: {node.DisplayName}, IsFolder: {node.IsFolder}, IsLoaded: {node.IsLoaded}");

            if (DataContext is ObjectExplorerViewModel viewModel)
            {
                // Load children if it's a folder and either not loaded or only has placeholder
                if (node.IsFolder && (!node.IsLoaded || HasOnlyPlaceholder(node)))
                {
                    System.Diagnostics.Debug.WriteLine($"Attempting to load node: {node.DisplayName}");

                    if (viewModel.ExpandNodeCommand.CanExecute(node))
                    {
                        System.Diagnostics.Debug.WriteLine($"Command can execute, executing for node: {node.DisplayName}");

                        // Execute the async command properly
                        if (viewModel.ExpandNodeCommand is AsyncRelayCommand<DatabaseObjectNode> asyncCommand)
                        {
                            _ = asyncCommand.ExecuteAsync(node);
                        }
                        else
                        {
                            viewModel.ExpandNodeCommand.Execute(node);
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"Command cannot execute for node: {node.DisplayName}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Node {node.DisplayName} - IsFolder: {node.IsFolder}, IsLoaded: {node.IsLoaded}, HasOnlyPlaceholder: {HasOnlyPlaceholder(node)}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("DataContext is not ObjectExplorerViewModel");
            }
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"Event source is not TreeViewItem or DataContext is not DatabaseObjectNode - Source: {e.Source?.GetType().Name}");
        }
    }

    /// <summary>
    /// Check if node has only placeholder children
    /// </summary>
    private bool HasOnlyPlaceholder(DatabaseObjectNode node)
    {
        return node.Children.Count == 1 && node.Children[0].IsPlaceholder;
    }

    /// <summary>
    /// Handle keyboard shortcuts
    /// </summary>
    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (DataContext is ObjectExplorerViewModel viewModel)
        {
            switch (e.Key)
            {
                case Key.F5:
                    // F5 to refresh
                    if (viewModel.RefreshCommand.CanExecute(null))
                    {
                        viewModel.RefreshCommand.Execute(null);
                        e.Handled = true;
                    }
                    break;
                    
                case Key.Enter:
                    // Enter to open table data or expand folder
                    if (viewModel.SelectedNode != null)
                    {
                        if (viewModel.SelectedNode.IsTableObject)
                        {
                            if (viewModel.OpenTableDataCommand.CanExecute(viewModel.SelectedNode))
                            {
                                viewModel.OpenTableDataCommand.Execute(viewModel.SelectedNode);
                                e.Handled = true;
                            }
                        }
                        else if (viewModel.SelectedNode.IsFolder)
                        {
                            viewModel.SelectedNode.IsExpanded = !viewModel.SelectedNode.IsExpanded;
                            e.Handled = true;
                        }
                    }
                    break;
                    
                case Key.Delete:
                    // Delete key could be used for object operations in the future
                    break;
            }
        }

        base.OnKeyDown(e);
    }

    /// <summary>
    /// 找到指定類型的父控制項
    /// </summary>
    private static T? FindParent<T>(DependencyObject child) where T : DependencyObject
    {
        var parentObject = VisualTreeHelper.GetParent(child);

        if (parentObject == null) return null;

        if (parentObject is T parent)
            return parent;

        return FindParent<T>(parentObject);
    }

    /// <summary>
    /// 保存當前的展開狀態
    /// </summary>
    private Dictionary<string, bool> SaveExpandedStates()
    {
        var states = new Dictionary<string, bool>();
        if (DataContext is ObjectExplorerViewModel viewModel)
        {
            SaveNodeExpandedStates(viewModel.RootNodes, states);
        }
        return states;
    }

    /// <summary>
    /// 遞歸保存節點展開狀態
    /// </summary>
    private void SaveNodeExpandedStates(IEnumerable<DatabaseObjectNode> nodes, Dictionary<string, bool> states)
    {
        foreach (var node in nodes)
        {
            var key = $"{node.ObjectType}_{node.DisplayName}";
            states[key] = node.IsExpanded;

            if (node.Children.Any())
            {
                SaveNodeExpandedStates(node.Children, states);
            }
        }
    }

    /// <summary>
    /// 恢復展開狀態
    /// </summary>
    private void RestoreExpandedStates(Dictionary<string, bool> states)
    {
        if (DataContext is ObjectExplorerViewModel viewModel)
        {
            RestoreNodeExpandedStates(viewModel.RootNodes, states);
        }
    }

    /// <summary>
    /// 遞歸恢復節點展開狀態
    /// </summary>
    private void RestoreNodeExpandedStates(IEnumerable<DatabaseObjectNode> nodes, Dictionary<string, bool> states)
    {
        foreach (var node in nodes)
        {
            var key = $"{node.ObjectType}_{node.DisplayName}";
            if (states.TryGetValue(key, out var isExpanded))
            {
                node.IsExpanded = isExpanded;
            }

            if (node.Children.Any())
            {
                RestoreNodeExpandedStates(node.Children, states);
            }
        }
    }

    #region ContextMenu 事件處理方法

    private void OnViewTableData(DatabaseObjectNode node)
    {
        if (DataContext is ObjectExplorerViewModel viewModel && viewModel.OpenTableDataCommand?.CanExecute(node) == true)
        {
            viewModel.OpenTableDataCommand.Execute(node);
        }
    }

    private void OnDesignTable(DatabaseObjectNode node)
    {
        if (DataContext is ObjectExplorerViewModel viewModel && viewModel.OpenTableDesignCommand?.CanExecute(node) == true)
        {
            viewModel.OpenTableDesignCommand.Execute(node);
        }
    }

    private void OnGenerateCreateScript(DatabaseObjectNode node)
    {
        if (DataContext is ObjectExplorerViewModel viewModel && viewModel.GenerateCreateScriptCommand?.CanExecute(node) == true)
        {
            viewModel.GenerateCreateScriptCommand.Execute(node);
        }
    }

    private void OnGenerateInsertScript(DatabaseObjectNode node)
    {
        if (DataContext is ObjectExplorerViewModel viewModel && viewModel.GenerateInsertScriptCommand?.CanExecute(node) == true)
        {
            viewModel.GenerateInsertScriptCommand.Execute(node);
        }
    }

    private void OnGenerateDropScript(DatabaseObjectNode node)
    {
        if (DataContext is ObjectExplorerViewModel viewModel && viewModel.GenerateDropScriptCommand?.CanExecute(node) == true)
        {
            viewModel.GenerateDropScriptCommand.Execute(node);
        }
    }

    private void OnCopyObjectName(DatabaseObjectNode node)
    {
        if (DataContext is ObjectExplorerViewModel viewModel && viewModel.CopyObjectNameCommand?.CanExecute(node) == true)
        {
            viewModel.CopyObjectNameCommand.Execute(node);
        }
    }

    private void OnRefresh()
    {
        if (DataContext is ObjectExplorerViewModel viewModel && viewModel.RefreshCommand?.CanExecute(null) == true)
        {
            viewModel.RefreshCommand.Execute(null);
        }
    }

    #endregion
}